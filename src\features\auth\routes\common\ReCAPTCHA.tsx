// components/ReCAPTCHAComponent.tsx
import React, { Dispatch, SetStateAction } from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "react-google-recaptcha";

const ReCAPTCHAComponent: React.FC<{
  setCaptchaVerified: Dispatch<SetStateAction<boolean>>;
}> = ({ setCaptchaVerified }) => {
  const onChange = (value: string | null) => {
    if (value) {
      setCaptchaVerified(true);
    } else {
      setCaptchaVerified(false);
    }
  };

  return (
    <div className="mt-4">
      {/* <ReCAPTCHA
        sitekey="6Lcu7hMrAAAAAIdmou9qJV5s3vCRic1qvZPdgA1x"
        onChange={handleCaptcha}
      /> */}
      <ReCAPTCHA
        sitekey="6Ld176wrAAAAAA8PJRInii1S5hz7dovVxINOHSoN"
        onChange={onChange}
      />
    </div>
  );
};

export default ReCAPTCHAComponent;
